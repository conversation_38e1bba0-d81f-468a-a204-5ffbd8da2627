/* 
 * Localizable.strings (Chinese Simplified)
 * ztt2
 * 
 * 转团团应用中文本地化文件
 */

// MARK: - 通用按钮文本
"common.button.cancel" = "取消";
"common.button.confirm" = "确定";
"common.button.delete" = "删除";
"common.button.save" = "保存";
"common.button.close" = "关闭";

// MARK: - 导航和标签页
"tab.home" = "首页";
"tab.diary" = "成长日记";
"tab.profile" = "个人中心";
"tab.current_selection_format" = "当前选中: %d";

// MARK: - 首页（家庭成员管理）
"home.title" = "转团团";
"home.button.add_member" = "添加成员";
"home.button.lottery_config" = "抽奖道具配置";
"home.button.family_operation" = "全员操作";
"home.button.family_total_score" = "全家一共加分";
"home.total_score.title" = "全家一共加分";
"home.member.empty.title" = "暂无成员";
"home.member.empty.description" = "点击\"添加成员\"按钮开始添加成员";

// MARK: - 成员管理
"member.delete.title" = "删除成员";
"member.delete.message" = "确定要删除成员 \"%@\" 吗？";
"member.delete.confirm" = "确定删除";
"member.info.unknown_name" = "未知";
"member.info.default_number" = "000";
"member.info.family_info" = "我的家庭";

// MARK: - 成长日记页面
"growth_diary.title" = "成长日记";
"growth_diary.input.placeholder" = "记录今天的成长点滴...";
"growth_diary.button.save" = "保存日记";
"growth_diary.button.view_history" = "查看历史日记";
"growth_diary.date.label" = "记录时间";
"growth_diary.save.success" = "日记保存成功";
"growth_diary.save.error" = "保存失败，请重试";
"growth_diary.input.empty" = "请输入日记内容";

// 选择对象功能
"growth_diary.member.label" = "选择对象";
"growth_diary.member.select" = "选择孩子";
"growth_diary.member.title" = "选择记录对象";
"growth_diary.member.empty" = "还没有添加孩子\n请先在首页添加家庭成员";
"growth_diary.member.picker.title" = "选择记录对象";

// 日期选择器
"growth_diary.date.picker.title" = "选择日期";

// 历史记录
"growth_diary.history.title" = "选择成员";
"growth_diary.history.empty" = "还没有添加孩子\n请先在首页添加家庭成员";

// 报告类型
"growth_diary.report.analysis" = "分析报告";
"growth_diary.report.growth" = "成长报告";
"growth_diary.report.analysis.title" = "积分分析报告";
"growth_diary.report.analysis.description" = "基于积分记录生成的行为分析报告，帮助了解孩子的表现趋势";
"growth_diary.report.growth.title" = "成长日记报告";
"growth_diary.report.growth.description" = "基于日记内容生成的成长分析报告，深入了解孩子的内心世界";

// MARK: - 个人中心页面
"profile.title" = "个人中心";
"profile.user_info.title" = "用户信息";
"profile.subscription.title" = "订阅管理";
"profile.subscription.current_status" = "当前状态";
"profile.subscription.upgrade" = "升级会员";
"profile.subscription.benefits" = "权益说明";
"profile.settings.title" = "系统设置";
"profile.settings.language" = "语言切换";
"profile.settings.history" = "历史生成记录";
"profile.settings.help" = "帮助与反馈";
"profile.settings.about" = "关于";
"profile.settings.delete_account" = "删除账号";
"profile.settings.logout" = "退出登录";

// MARK: - 占位页面文本
"placeholder.home.title" = "首页";
"placeholder.diary.title" = "成长日记";
"placeholder.profile.title" = "个人中心";
"placeholder.developing" = "功能开发中...";

// MARK: - 错误信息
"error.network" = "网络连接失败";
"error.unknown" = "未知错误";
"error.permission_denied" = "权限不足";

// MARK: - 成员详情页面
"member_detail.title" = "成员详情";
"member_detail.action.add_points" = "加分";
"member_detail.action.deduct_points" = "扣分";
"member_detail.action.exchange" = "兑换";
"member_detail.action.lottery" = "抽奖";
"member_detail.action.analysis" = "分析";
"member_detail.history.points" = "积分记录";
"member_detail.history.exchange" = "兑换记录";
"member_detail.history.empty.title" = "暂无记录";
"member_detail.history.empty.description" = "学生的积分变动记录将在此显示";
"member_detail.info.role_age_format" = "%@ · %d岁";

// MARK: - 日期范围选择
"date_range.this_week" = "本周";
"date_range.this_month" = "本月";
"date_range.custom" = "自定义";
"date_range.select_range" = "选择时间范围";
"date_range.start_date" = "开始日期";
"date_range.end_date" = "结束日期";
"date_range.invalid_range" = "结束日期不能早于开始日期";
"date_range.total_score_format" = "%@全家一共加分";

// MARK: - 抽奖功能
"lottery.menu.title" = "选择抽奖道具";
"lottery.menu.wheel" = "大转盘";
"lottery.menu.wheel_description" = "转动轮盘获得随机奖品";
"lottery.menu.blind_box" = "盲盒";
"lottery.menu.blind_box_description" = "开启盲盒发现惊喜";
"lottery.menu.scratch_card" = "刮刮卡";
"lottery.menu.scratch_card_description" = "刮开卡片揭晓奖品";

// MARK: - 抽奖权限提示
"lottery.permission.wheel_title" = "大转盘需要初级会员";
"lottery.permission.wheel_message" = "升级到初级会员即可使用大转盘功能";
"lottery.permission.advanced_title" = "高级功能需要高级会员";
"lottery.permission.advanced_message" = "升级到高级会员即可使用盲盒和刮刮卡功能";

// MARK: - 订阅相关
"subscription.upgrade" = "升级会员";
"common.cancel" = "取消";

// MARK: - 抽奖道具配置
"lottery_config.title" = "抽奖道具配置";
"lottery_config.wheel.title" = "大转盘配置";
"lottery_config.blindbox.title" = "盲盒配置";
"lottery_config.scratchcard.title" = "刮刮卡配置";

// 通用配置项
"lottery_config.cost_per_play" = "每次消耗积分";
"lottery_config.cost_per_play.placeholder" = "请输入积分";
"lottery_config.item_count" = "数量设置";
"lottery_config.prizes" = "奖品设置";

// 大转盘配置
"lottery_config.wheel.sector_count" = "分区数量";
"lottery_config.wheel.sector_count.range" = "4-12个分区";
"lottery_config.wheel.sector_format" = "分区 %d";
"lottery_config.wheel.sector_prize" = "分区奖品";

// 盲盒配置
"lottery_config.blindbox.count" = "盲盒数量";
"lottery_config.blindbox.count.range" = "2-20个盲盒";
"lottery_config.blindbox.item_format" = "盲盒 %d";
"lottery_config.blindbox.prize" = "盲盒奖品";

// 刮刮卡配置
"lottery_config.scratchcard.count" = "刮刮卡数量";
"lottery_config.scratchcard.count.range" = "2-20张刮刮卡";
"lottery_config.scratchcard.item_format" = "刮刮卡 %d";
"lottery_config.scratchcard.prize" = "刮刮卡奖品";

// 配置验证
"lottery_config.validation.empty_prize" = "请填写所有奖品名称";
"lottery_config.validation.invalid_cost" = "请输入有效的积分数值";
"lottery_config.validation.cost_range" = "积分必须大于0";
"lottery_config.validation.prize_too_long" = "奖品名称不能超过20个字符";

// 配置操作
"lottery_config.button.save" = "保存配置";
"lottery_config.button.cancel" = "取消";
"lottery_config.save.success" = "配置保存成功";
"lottery_config.save.error" = "保存失败，请重试";

// MARK: - 大转盘功能
"lottery_wheel.page_title" = "大转盘";
"lottery_wheel.back_button" = "返回";
"lottery_wheel.current_points" = "当前积分：%d";
"lottery_wheel.cost_info" = "每次抽奖消耗 %d 积分";
"lottery_wheel.affordable" = "可抽奖";
"lottery_wheel.insufficient_points" = "积分不足";
"lottery_wheel.spinning" = "转盘旋转中...";
"lottery_wheel.spin_button" = "开始抽奖";
"lottery_wheel.confirm_result" = "确认结果";
"lottery_wheel.mystery_prize" = "神秘奖品";
"lottery_wheel.empty_prize" = "空奖品";

// 大转盘状态提示
"lottery_wheel.no_config.title" = "未配置大转盘";
"lottery_wheel.no_config.description" = "请先配置大转盘奖品后再进行抽奖";
"lottery_wheel.go_settings" = "前往设置";
"lottery_wheel.empty_prizes.title" = "暂无奖品";
"lottery_wheel.empty_prizes.description" = "请先添加奖品后再进行抽奖";

// 积分不足提示
"lottery_wheel.insufficient_points_title" = "积分不足";
"lottery_wheel.insufficient_points_message" = "抽奖需要 %@ 积分，您当前只有 %@ 积分";

// MARK: - 盲盒功能
"blind_box.page_title" = "盲盒开启";
"blind_box.back_button" = "返回";
"blind_box.current_points" = "当前积分：%d";
"blind_box.cost_info" = "每次开启消耗 %d 积分";
"blind_box.loading_config" = "加载盲盒配置中...";
"blind_box.box_title" = "盲盒 %d";
"blind_box.status_opened" = "已开启";
"blind_box.status_waiting" = "待开启";
"blind_box.result_title" = "恭喜获得";
"blind_box.result_confirm" = "确认";
"blind_box.result_cost" = "消耗积分: %d";

// 盲盒状态提示
"blind_box.no_config.title" = "暂无盲盒配置";
"blind_box.no_config.description" = "请先配置盲盒道具";
"blind_box.go_settings" = "前往配置";
"blind_box.empty.title" = "盲盒为空";
"blind_box.empty.description" = "请检查盲盒配置";

// 盲盒积分不足提示
"blind_box.insufficient_points_title" = "积分不足";
"blind_box.insufficient_points_message" = "开启盲盒需要 %d 积分\n当前积分: %d";
"blind_box.insufficient_points_confirm" = "确定";

// 盲盒奖品
"blind_box.mystery_prize" = "神秘奖品";
"blind_box.opening_success" = "盲盒开启成功";
"blind_box.opening_failed" = "盲盒开启失败";

// MARK: - 成功信息
"success.save" = "保存成功";
"success.delete" = "删除成功";
"success.update" = "更新成功";
