//
//  ExplosionAnimationView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 爆炸动画视图组件
 * 实现盲盒开启时的爆炸分解效果
 */
struct ExplosionAnimationView: View {
    
    // MARK: - Properties
    let boxItem: BlindBoxItem
    let isActive: Bool
    
    // MARK: - State
    @State private var faces: [CubeFace] = []
    @State private var explosionProgress: Double = 0
    @State private var showFlash = false
    @State private var showShockwave = false
    @State private var shockwaveScale: CGFloat = 0
    
    // MARK: - Constants
    private let maxScatterDistance: CGFloat = 150
    private let explosionDuration: Double = 1.1
    
    var body: some View {
        ZStack {
            // 面片散射效果
            ForEach(faces) { face in
                faceView(face: face)
            }
            
            // 闪光效果
            if showFlash {
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [Color.white, Color.yellow.opacity(0.8), Color.clear],
                            center: .center,
                            startRadius: 0,
                            endRadius: 50
                        )
                    )
                    .frame(width: 100, height: 100)
                    .opacity(showFlash ? 0.8 : 0)
                    .scaleEffect(showFlash ? 1.5 : 0.5)
                    .animation(.easeOut(duration: 0.2), value: showFlash)
            }
            
            // 冲击波效果
            if showShockwave {
                Circle()
                    .stroke(Color.yellow.opacity(0.6), lineWidth: 3)
                    .frame(width: 80, height: 80)
                    .scaleEffect(shockwaveScale)
                    .opacity(1 - Double(shockwaveScale / 3))
                    .animation(.easeOut(duration: 0.5), value: shockwaveScale)
            }
        }
        .onAppear {
            setupFaces()
        }
        .onChange(of: isActive) { active in
            if active {
                executeExplosionSequence()
            }
        }
    }
    
    // MARK: - Face View
    
    private func faceView(face: CubeFace) -> some View {
        RoundedRectangle(cornerRadius: 4)
            .fill(
                LinearGradient(
                    colors: [Color.blue.opacity(0.8), Color.cyan.opacity(0.6)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(width: 20, height: 20)
            .offset(face.offset)
            .rotationEffect(.degrees(face.rotation))
            .scaleEffect(face.scale)
            .opacity(1 - explosionProgress)
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置立方体面片
     */
    private func setupFaces() {
        faces = CubeFace.FaceType.allCases.map { type in
            CubeFace(type: type)
        }
    }
    
    /**
     * 执行爆炸动画序列
     */
    private func executeExplosionSequence() {
        // 阶段1: 闪光效果 (0.1秒)
        withAnimation(.easeOut(duration: 0.1)) {
            showFlash = true
        }
        
        // 阶段2: 开始面片飞散 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeOut(duration: 0.3)) {
                self.scatterFaces()
                self.explosionProgress = 0.4
            }
            
            // 显示冲击波
            self.showShockwave = true
            withAnimation(.easeOut(duration: 0.5)) {
                self.shockwaveScale = 3
            }
        }
        
        // 阶段3: 继续扩散 (0.4秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeInOut(duration: 0.4)) {
                self.continueFaceScattering()
                self.explosionProgress = 0.8
            }
        }
        
        // 阶段4: 消失 (0.3秒)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            withAnimation(.easeIn(duration: 0.3)) {
                self.explosionProgress = 1.0
                self.hideFaces()
            }
        }
        
        // 清理效果
        DispatchQueue.main.asyncAfter(deadline: .now() + explosionDuration) {
            self.resetEffects()
        }
    }
    
    /**
     * 散射面片
     */
    private func scatterFaces() {
        for i in faces.indices {
            let angle = Double(i) * (2 * .pi / Double(faces.count))
            let distance = maxScatterDistance * 0.6
            
            faces[i].offset = CGPoint(
                x: cos(angle) * distance,
                y: sin(angle) * distance
            )
            faces[i].rotation = Double.random(in: 0...360)
            faces[i].scale = 0.8
        }
    }
    
    /**
     * 继续面片散射
     */
    private func continueFaceScattering() {
        for i in faces.indices {
            let currentOffset = faces[i].offset
            faces[i].offset = CGPoint(
                x: currentOffset.x * 1.5,
                y: currentOffset.y * 1.5
            )
            faces[i].rotation += Double.random(in: 180...360)
            faces[i].scale = 0.4
        }
    }
    
    /**
     * 隐藏面片
     */
    private func hideFaces() {
        for i in faces.indices {
            faces[i].scale = 0.1
        }
    }
    
    /**
     * 重置效果
     */
    private func resetEffects() {
        showFlash = false
        showShockwave = false
        shockwaveScale = 0
        explosionProgress = 0
        setupFaces()
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 40) {
        ExplosionAnimationView(
            boxItem: BlindBoxItem.create(index: 0, prizeName: "测试奖品"),
            isActive: true
        )
        .frame(width: 200, height: 200)
        .background(Color.gray.opacity(0.1))
    }
    .padding()
}
