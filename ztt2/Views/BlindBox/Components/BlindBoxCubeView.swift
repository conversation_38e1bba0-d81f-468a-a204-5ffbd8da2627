//
//  BlindBoxCubeView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒立方体视图组件
 * 实现3D立方体效果和点击动画
 */
struct BlindBoxCubeView: View {
    
    // MARK: - Properties
    let boxItem: BlindBoxItem
    let onTap: () -> Void
    
    // MARK: - State
    @State private var rotationAngle: Double = 0
    @State private var isPressed = false
    
    var body: some View {
        ZStack {
            // 主立方体
            cubeBody
                .rotation3DEffect(
                    .degrees(rotationAngle),
                    axis: (x: 0.2, y: 1.0, z: 0.1),
                    anchor: .center,
                    perspective: 0.8
                )
                .scaleEffect(boxItem.scaleEffect * (isPressed ? 0.95 : 1.0))
                .opacity(boxItem.opacity)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
                .animation(.easeInOut(duration: 0.8), value: boxItem.explosionState)
                .onTapGesture {
                    if boxItem.isClickable {
                        // 触觉反馈
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                        
                        // 点击动画
                        withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                            rotationAngle += 360
                        }
                        
                        onTap()
                    }
                }
                .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = pressing
                    }
                }, perform: {})
        }
    }
    
    // MARK: - Cube Body
    
    private var cubeBody: some View {
        ZStack {
            // 根据盲盒状态显示不同图片
            if boxItem.isOpened {
                // 已打开状态
                Image("宝箱已打开")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 80, height: 80)
            } else {
                // 未打开状态
                Image("宝箱未打开")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 80, height: 80)
            }
            
            // 奖品名称显示（仅在已开启时显示）
            if boxItem.isOpened && !boxItem.prizeName.isEmpty {
                VStack {
                    Spacer()
                    Text(boxItem.prizeName)
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.black.opacity(0.7))
                        )
                        .offset(y: 10)
                }
            }
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // 未开启的盲盒
        BlindBoxCubeView(
            boxItem: BlindBoxItem.create(index: 0, prizeName: "神秘奖品"),
            onTap: {
                print("点击盲盒")
            }
        )
        
        // 已开启的盲盒
        BlindBoxCubeView(
            boxItem: BlindBoxItem.createWithPrize(
                index: 1,
                prizeName: "小红花",
                isOpened: true,
                explosionState: .completed
            ),
            onTap: {
                print("点击已开启盲盒")
            }
        )
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
