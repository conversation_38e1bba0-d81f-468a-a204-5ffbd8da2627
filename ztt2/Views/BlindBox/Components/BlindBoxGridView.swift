//
//  BlindBoxGridView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒网格视图组件
 * 负责盲盒的网格布局和动画展示
 */
struct BlindBoxGridView: View {
    
    // MARK: - Properties
    @ObservedObject var viewModel: BlindBoxViewModel
    let geometry: GeometryProxy
    let onBoxTapped: (Int) -> Void
    
    // MARK: - Constants
    private let columns = 2
    private let spacing: CGFloat = 20
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: gridColumns, spacing: spacing) {
                ForEach(Array(viewModel.boxItems.enumerated()), id: \.element.id) { index, boxItem in
                    blindBoxCell(boxItem: boxItem, index: index)
                        .onAppear {
                            // 设置盲盒位置（用于粒子效果）
                            updateBoxPosition(at: index)
                        }
                }
            }
            .padding(.horizontal, spacing)
            .padding(.vertical, 20)
        }
    }
    
    // MARK: - Grid Columns
    
    private var gridColumns: [GridItem] {
        Array(repeating: GridItem(.flexible(), spacing: spacing), count: columns)
    }
    
    // MARK: - Blind Box Cell
    
    private func blindBoxCell(boxItem: BlindBoxItem, index: Int) -> some View {
        VStack(spacing: 12) {
            // 盲盒容器
            ZStack {
                // 悬浮动画包装
                FloatingAnimationView(
                    isEnabled: !boxItem.isOpened,
                    floatingOffset: 8,
                    animationDuration: boxItem.floatingDuration,
                    animationDelay: boxItem.floatingDelay,
                    rotationAngle: 3
                ) {
                    // 盲盒立方体
                    BlindBoxCubeView(
                        boxItem: boxItem,
                        onTap: {
                            onBoxTapped(index)
                        }
                    )
                }
                
                // 爆炸动画覆盖层
                if let explosionState = viewModel.animationStates[boxItem.id],
                   explosionState == .exploding {
                    ExplosionAnimationView(
                        boxItem: boxItem,
                        isActive: true
                    )
                }
            }
            .frame(width: boxCellSize, height: boxCellSize)
            
            // 盲盒标题
            Text(boxItem.displayTitle)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
                .opacity(boxItem.isOpened ? 0.6 : 1.0)
            
            // 状态指示器
            statusIndicator(for: boxItem)
        }
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(boxItem.isOpened ? 0.1 : 0.15))
                .backdrop(blur: 10)
        )
        .scaleEffect(boxItem.isOpened ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.3), value: boxItem.isOpened)
    }
    
    // MARK: - Status Indicator
    
    private func statusIndicator(for boxItem: BlindBoxItem) -> some View {
        HStack(spacing: 4) {
            Circle()
                .fill(boxItem.isOpened ? Color.green : Color.yellow)
                .frame(width: 8, height: 8)
            
            Text(boxItem.isOpened ? "blind_box.status_opened".localized : "blind_box.status_waiting".localized)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
        }
    }
    
    // MARK: - Computed Properties
    
    private var boxCellSize: CGFloat {
        let availableWidth = geometry.size.width - (spacing * 3) // 左右边距 + 中间间距
        return availableWidth / CGFloat(columns)
    }
    
    // MARK: - Private Methods
    
    private func updateBoxPosition(at index: Int) {
        let row = index / columns
        let col = index % columns
        
        let cellWidth = boxCellSize + spacing
        let cellHeight = boxCellSize + 60 // 包含标题和状态的高度
        
        let x = CGFloat(col) * cellWidth + cellWidth / 2 + spacing
        let y = CGFloat(row) * cellHeight + cellHeight / 2 + 100 // 考虑顶部偏移
        
        // 更新viewModel中的位置信息（用于粒子效果）
        if index < viewModel.boxItems.count {
            viewModel.boxItems[index].position = CGPoint(x: x, y: y)
        }
    }
}

// MARK: - Preview

#Preview {
    GeometryReader { geometry in
        BlindBoxGridView(
            viewModel: {
                let member = Member(context: DataManager.shared.context)
                member.name = "测试成员"
                member.points = 100
                return BlindBoxViewModel(member: member)
            }(),
            geometry: geometry,
            onBoxTapped: { index in
                print("点击盲盒 \(index)")
            }
        )
    }
    .background(
        LinearGradient(
            colors: [Color.blue, Color.purple],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    )
}
