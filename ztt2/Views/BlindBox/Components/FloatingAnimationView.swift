//
//  FloatingAnimationView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 悬浮动画视图组件
 * 为盲盒添加轻微的上下浮动效果
 */
struct FloatingAnimationView<Content: View>: View {
    
    // MARK: - Properties
    let content: Content
    let isEnabled: Bool
    let floatingOffset: CGFloat
    let animationDuration: Double
    let animationDelay: Double
    let rotationAngle: Double
    
    // MARK: - State
    @State private var isFloating = false
    @State private var rotationState: Double = 0
    @State private var animationTimer: Timer?
    
    // MARK: - Initializer
    
    init(
        isEnabled: Bool = true,
        floatingOffset: CGFloat = 8,
        animationDuration: Double = 2.0,
        animationDelay: Double = 0,
        rotationAngle: Double = 3,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.isEnabled = isEnabled
        self.floatingOffset = floatingOffset
        self.animationDuration = animationDuration
        self.animationDelay = animationDelay
        self.rotationAngle = rotationAngle
    }
    
    var body: some View {
        content
            .offset(y: isEnabled ? (isFloating ? floatingOffset : -floatingOffset) : 0)
            .rotationEffect(.degrees(isEnabled ? rotationState : 0))
            .onAppear {
                if isEnabled {
                    startFloatingAnimation()
                }
            }
            .onDisappear {
                stopFloatingAnimation()
            }
    }
    
    // MARK: - Private Methods
    
    /**
     * 开始悬浮动画
     */
    private func startFloatingAnimation() {
        // 延迟启动动画，避免所有盲盒同时开始
        DispatchQueue.main.asyncAfter(deadline: .now() + animationDelay) {
            // 垂直浮动动画
            withAnimation(
                .easeInOut(duration: animationDuration)
                .repeatForever(autoreverses: true)
            ) {
                isFloating.toggle()
            }
            
            // 旋转动画（稍微不同的时机）
            withAnimation(
                .easeInOut(duration: animationDuration * 1.2)
                .repeatForever(autoreverses: true)
            ) {
                rotationState = rotationAngle
            }
        }
    }
    
    /**
     * 停止悬浮动画
     */
    private func stopFloatingAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
        
        withAnimation(.easeOut(duration: 0.3)) {
            isFloating = false
            rotationState = 0
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 40) {
        // 启用悬浮动画的盲盒
        FloatingAnimationView(
            isEnabled: true,
            floatingOffset: 10,
            animationDuration: 2.0,
            animationDelay: 0,
            rotationAngle: 5
        ) {
            Image("宝箱未打开")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 80, height: 80)
        }
        
        // 禁用悬浮动画的盲盒
        FloatingAnimationView(
            isEnabled: false
        ) {
            Image("宝箱已打开")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 80, height: 80)
        }
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
