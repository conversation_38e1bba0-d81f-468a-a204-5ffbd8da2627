//
//  ParticleSystemView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 粒子系统组件
 * 实现各种粒子效果，包括庆祝、爆炸、火花等
 */
struct ParticleSystemView: View {
    
    // MARK: - Properties
    let particles: [ParticleItem]
    let isActive: Bool
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                particleView(particle: particle)
            }
        }
        .allowsHitTesting(false) // 粒子不接收触摸事件
    }
    
    // MARK: - Particle View
    
    private func particleView(particle: ParticleItem) -> some View {
        Circle()
            .fill(particle.color)
            .frame(width: particle.size, height: particle.size)
            .position(particle.currentPosition)
            .opacity(particle.opacity)
            .scaleEffect(particle.opacity) // 根据生命周期缩放
            .blur(radius: (1 - particle.opacity) * 2) // 死亡时模糊
    }
}

/**
 * 盲盒结果展示视图
 * 显示开箱结果和庆祝动画
 */
struct BlindBoxResultView: View {
    
    // MARK: - Properties
    let prizeName: String
    let costPoints: Int
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    // MARK: - State
    @State private var showResult = false
    @State private var scaleEffect: CGFloat = 0.5
    @State private var rotationAngle: Double = 0
    @State private var celebrationParticles: [ParticleItem] = []
    @State private var particleTimer: Timer?
    
    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    onCancel()
                }
            
            // 主要内容
            VStack(spacing: 20) {
                // 标题
                Text("blind_box.result_title".localized)
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                
                // 奖品展示
                VStack(spacing: 12) {
                    // 奖品图标
                    Image("宝箱已打开")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 100, height: 100)
                        .rotationEffect(.degrees(rotationAngle))
                        .scaleEffect(scaleEffect)
                    
                    // 奖品名称
                    Text(prizeName)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.yellow.opacity(0.2))
                                .stroke(Color.yellow, lineWidth: 2)
                        )
                }
                
                // 消耗积分信息
                Text(String(format: "blind_box.result_cost".localized, costPoints))
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))

                // 确认按钮
                Button(action: onConfirm) {
                    Text("blind_box.result_confirm".localized)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        colors: [Color.green, Color.green.opacity(0.8)],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                        )
                }
                .padding(.horizontal, 40)
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            colors: [Color.purple.opacity(0.9), Color.blue.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: .black.opacity(0.3), radius: 20, x: 0, y: 10)
            )
            .scaleEffect(showResult ? 1.0 : 0.5)
            .opacity(showResult ? 1.0 : 0.0)
            
            // 庆祝粒子效果
            ParticleSystemView(particles: celebrationParticles, isActive: true)
        }
        .onAppear {
            startPresentationAnimation()
        }
        .onDisappear {
            stopCelebration()
        }
    }
    
    // MARK: - Animation Methods
    
    /**
     * 开始展示动画
     */
    private func startPresentationAnimation() {
        // 延迟显示内容
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                showResult = true
                scaleEffect = 1.0
            }
        }
        
        // 旋转入场
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.easeOut(duration: 0.6)) {
                rotationAngle = 360
            }
        }
        
        // 弹跳效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
                bounceEffect()
            }
        }
        
        // 启动庆祝动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            startCelebration()
        }
    }
    
    /**
     * 弹跳效果
     */
    private func bounceEffect() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.5)) {
            scaleEffect = 1.1
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                scaleEffect = 1.0
            }
        }
    }
    
    /**
     * 开始庆祝动画
     */
    private func startCelebration() {
        generateCelebrationParticles()
        
        particleTimer = Timer.scheduledTimer(withTimeInterval: 0.3, repeats: true) { _ in
            generateCelebrationParticles()
        }
    }
    
    /**
     * 停止庆祝动画
     */
    private func stopCelebration() {
        particleTimer?.invalidate()
        particleTimer = nil
        celebrationParticles.removeAll()
    }
    
    /**
     * 生成庆祝粒子
     */
    private func generateCelebrationParticles() {
        let screenCenter = CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2)
        let colors: [Color] = [.yellow, .orange, .pink, .purple, .blue, .green]
        
        for _ in 0..<15 {
            let angle = Double.random(in: 0...(2 * .pi))
            let speed = CGFloat.random(in: 100...200)
            let velocity = CGPoint(
                x: cos(angle) * speed,
                y: sin(angle) * speed
            )
            
            let particle = ParticleItem(
                startPosition: screenCenter,
                currentPosition: screenCenter,
                velocity: velocity,
                color: colors.randomElement() ?? .yellow,
                size: CGFloat.random(in: 4...8),
                lifespan: 2.0
            )
            
            celebrationParticles.append(particle)
        }
        
        // 清理过期粒子
        celebrationParticles.removeAll { !$0.isAlive }
    }
}

// MARK: - Preview

#Preview {
    BlindBoxResultView(
        prizeName: "小红花",
        costPoints: 10,
        onConfirm: {
            print("确认结果")
        },
        onCancel: {
            print("取消")
        }
    )
}
