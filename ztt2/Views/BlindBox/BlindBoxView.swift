//
//  BlindBoxView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒主视图
 * 实现完整的盲盒游戏界面，包括网格布局、动画效果、结果展示等
 */
struct BlindBoxView: View {
    
    // MARK: - Properties
    @StateObject private var viewModel: BlindBoxViewModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    
    let member: Member
    let onNavigateToSettings: (() -> Void)?
    
    // MARK: - Initializer
    
    init(member: Member, onNavigateToSettings: (() -> Void)? = nil) {
        self.member = member
        self.onNavigateToSettings = onNavigateToSettings
        self._viewModel = StateObject(wrappedValue: BlindBoxViewModel(member: member))
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                backgroundGradient
                
                // 主要内容
                mainContent(geometry: geometry)
                
                // 结果弹窗
                if viewModel.showResult {
                    resultOverlay
                }
                
                // 积分不足提示
                if viewModel.showInsufficientPoints {
                    insufficientPointsAlert
                }
                
                // 粒子效果层
                particleEffectLayer
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("blind_box.page_title".localized)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                backButton
            }
        }
        .onAppear {
            viewModel.loadBlindBoxConfig()
        }
    }
    
    // MARK: - Background
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(hex: "#667eea"),
                Color(hex: "#764ba2")
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Back Button
    
    private var backButton: some View {
        Button(action: {
            dismiss()
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .semibold))
                Text("blind_box.back_button".localized)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.white)
        }
    }
    
    // MARK: - Main Content
    
    @ViewBuilder
    private func mainContent(geometry: GeometryProxy) -> some View {
        VStack(spacing: 20) {
            // 顶部信息栏
            topInfoBar
            
            // 内容区域
            contentArea(geometry: geometry)
            
            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    // MARK: - Top Info Bar
    
    private var topInfoBar: some View {
        HStack {
            // 成员信息
            VStack(alignment: .leading, spacing: 4) {
                Text(member.name ?? "未知成员")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Text(String(format: "blind_box.current_points".localized, Int(member.points)))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
            
            // 消耗积分信息
            VStack(alignment: .trailing, spacing: 4) {
                Text("每次消耗")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))

                Text(String(format: "blind_box.cost_info".localized, viewModel.costPerOpen))
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.yellow)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.2))
                .backdrop(blur: 10)
        )
    }
    
    // MARK: - Content Area
    
    @ViewBuilder
    private func contentArea(geometry: GeometryProxy) -> some View {
        if viewModel.isLoading {
            loadingView
        } else if viewModel.showNoConfig {
            noConfigView
        } else if viewModel.boxItems.isEmpty {
            emptyStateView
        } else {
            blindBoxGridView(geometry: geometry)
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
            
            Text("blind_box.loading_config".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - No Config View
    
    private var noConfigView: some View {
        VStack(spacing: 20) {
            Image(systemName: "shippingbox")
                .font(.system(size: 60))
                .foregroundColor(.white.opacity(0.6))
            
            Text("blind_box.no_config.title".localized)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)

            Text("blind_box.no_config.description".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.8))

            Button(action: {
                onNavigateToSettings?()
            }) {
                Text("blind_box.go_settings".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.white.opacity(0.2))
                    )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Empty State View

    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "questionmark.square.dashed")
                .font(.system(size: 60))
                .foregroundColor(.white.opacity(0.6))

            Text("blind_box.empty.title".localized)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)

            Text("blind_box.empty.description".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - Blind Box Grid View

    private func blindBoxGridView(geometry: GeometryProxy) -> some View {
        BlindBoxGridView(
            viewModel: viewModel,
            geometry: geometry,
            onBoxTapped: { index in
                handleBoxTapped(at: index)
            }
        )
    }

    // MARK: - Result Overlay

    private var resultOverlay: some View {
        BlindBoxResultView(
            prizeName: viewModel.resultPrize,
            costPoints: viewModel.costPerOpen,
            onConfirm: {
                viewModel.confirmResult()
            },
            onCancel: {
                viewModel.showResult = false
                viewModel.resultPrize = ""
            }
        )
        .zIndex(1000)
    }

    // MARK: - Insufficient Points Alert

    private var insufficientPointsAlert: some View {
        ZStack {
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    viewModel.showInsufficientPoints = false
                }

            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 50))
                    .foregroundColor(.orange)

                Text("blind_box.insufficient_points_title".localized)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.white)

                Text(String(format: "blind_box.insufficient_points_message".localized, viewModel.costPerOpen, Int(member.points)))
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)

                Button(action: {
                    viewModel.showInsufficientPoints = false
                }) {
                    Text("blind_box.insufficient_points_confirm".localized)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.orange)
                        )
                }
                .padding(.horizontal, 40)
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.black.opacity(0.8))
                    .backdrop(blur: 20)
            )
            .padding(.horizontal, 40)
        }
    }

    // MARK: - Particle Effect Layer

    private var particleEffectLayer: some View {
        ParticleSystemView(particles: viewModel.particles, isActive: true)
            .allowsHitTesting(false)
    }

    // MARK: - Actions

    private func handleBoxTapped(at index: Int) {
        let success = viewModel.openBlindBox(at: index)
        if success {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
            impactFeedback.impactOccurred()
        }
    }
}
