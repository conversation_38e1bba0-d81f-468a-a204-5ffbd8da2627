//
//  BlindBoxTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒功能测试视图
 * 用于测试盲盒动画效果、交互逻辑等功能
 */
struct BlindBoxTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var showBlindBox = false
    @State private var testMember: Member?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                Text("盲盒功能测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.primary)
                
                // 测试说明
                VStack(alignment: .leading, spacing: 12) {
                    Text("测试功能:")
                        .font(.system(size: 18, weight: .semibold))
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("• 盲盒3D立方体效果")
                        Text("• 悬浮动画")
                        Text("• 爆炸动画")
                        Text("• 粒子效果")
                        Text("• 开箱逻辑")
                        Text("• 积分扣除")
                        Text("• 本地化字符串")
                    }
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.1))
                )
                
                // 测试成员信息
                if let member = testMember {
                    VStack(spacing: 8) {
                        Text("测试成员: \(member.name ?? "未知")")
                            .font(.system(size: 16, weight: .medium))
                        
                        Text("当前积分: \(Int(member.points))")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.blue.opacity(0.1))
                    )
                }
                
                // 测试按钮
                VStack(spacing: 16) {
                    Button(action: {
                        createTestMember()
                    }) {
                        Text("创建测试成员")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.blue)
                            )
                    }
                    
                    Button(action: {
                        createTestBlindBoxConfig()
                    }) {
                        Text("创建测试盲盒配置")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.green)
                            )
                    }
                    .disabled(testMember == nil)
                    
                    Button(action: {
                        showBlindBox = true
                    }) {
                        Text("启动盲盒测试")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.purple)
                            )
                    }
                    .disabled(testMember == nil)
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .navigationTitle("盲盒测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .fullScreenCover(isPresented: $showBlindBox) {
            if let member = testMember {
                NavigationView {
                    BlindBoxView(member: member)
                }
                .environmentObject(dataManager)
            }
        }
        .onAppear {
            loadTestMember()
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 加载测试成员
     */
    private func loadTestMember() {
        let members = dataManager.getAllMembers()
        testMember = members.first { $0.name == "盲盒测试成员" }
    }
    
    /**
     * 创建测试成员
     */
    private func createTestMember() {
        // 删除现有的测试成员
        if let existingMember = testMember {
            dataManager.deleteMember(existingMember)
        }
        
        // 创建新的测试成员
        let member = dataManager.addMember(
            name: "盲盒测试成员",
            role: "儿子",
            age: 10,
            points: 1000 // 给足够的积分进行测试
        )
        
        testMember = member
        print("✅ 创建测试成员成功: \(member?.name ?? "未知"), 积分: \(member?.points ?? 0)")
    }
    
    /**
     * 创建测试盲盒配置
     */
    private func createTestBlindBoxConfig() {
        guard let member = testMember else { return }
        
        // 删除现有配置
        if let existingConfig = dataManager.getBlindBoxConfig(for: member) {
            dataManager.deleteBlindBoxConfig(existingConfig)
        }
        
        // 创建测试配置
        let configData = BlindBoxConfigData(
            itemCount: 6,
            costPerPlay: 10,
            prizes: [
                "小红花",
                "贴纸",
                "糖果",
                "玩具",
                "书本",
                "奖状"
            ]
        )
        
        let success = dataManager.saveBlindBoxConfig(for: member, configData: configData)
        
        if success {
            print("✅ 创建测试盲盒配置成功: 6个盲盒，每次消耗10积分")
        } else {
            print("❌ 创建测试盲盒配置失败")
        }
    }
}

// MARK: - Preview

#Preview {
    BlindBoxTestView()
        .environmentObject(DataManager.shared)
}
