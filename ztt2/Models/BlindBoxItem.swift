//
//  BlindBoxItem.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 盲盒项目数据模型
 * 用于管理单个盲盒的状态、动画和位置信息
 */
struct BlindBoxItem: Identifiable {
    
    // MARK: - Identifiable
    let id = UUID()
    
    // MARK: - Basic Properties
    let index: Int
    let prizeName: String
    
    // MARK: - State Properties
    var isOpened: Bool = false
    var openingProgress: Double = 0.0
    
    // MARK: - Animation Properties
    var position: CGPoint = .zero
    var floatingOffset: CGFloat = 0.0
    var rotationAngle: Double = 0.0
    var explosionState: ExplosionState = .idle
    var scaleEffect: CGFloat = 1.0
    
    // MARK: - Timing Properties
    var floatingDelay: Double {
        return Double(index) * 0.1
    }
    
    var floatingDuration: Double {
        return 0.8 + Double(index % 3) * 0.2
    }
    
    // MARK: - Computed Properties
    
    /**
     * 获取盲盒显示标题
     */
    var displayTitle: String {
        return "盲盒 \(index + 1)"
    }
    
    /**
     * 检查是否可以点击
     */
    var isClickable: Bool {
        return !isOpened && explosionState == .idle
    }
    
    /**
     * 获取当前透明度
     */
    var opacity: Double {
        switch explosionState {
        case .idle:
            return isOpened ? 0.5 : 1.0
        case .exploding:
            return 0.8
        case .completed:
            return 1.0
        }
    }
}

/**
 * 盲盒爆炸动画状态枚举
 */
enum ExplosionState: CaseIterable {
    case idle       // 静止状态
    case exploding  // 爆炸进行中
    case completed  // 爆炸完成
    
    var description: String {
        switch self {
        case .idle:
            return "静止"
        case .exploding:
            return "爆炸中"
        case .completed:
            return "完成"
        }
    }
}

/**
 * 粒子数据模型
 */
struct ParticleItem: Identifiable {
    let id = UUID()
    let startPosition: CGPoint
    var currentPosition: CGPoint
    let velocity: CGPoint
    let color: Color
    let size: CGFloat
    let lifespan: Double
    var age: Double = 0.0
    
    /**
     * 检查粒子是否还存活
     */
    var isAlive: Bool {
        return age < lifespan
    }
    
    /**
     * 获取当前透明度（基于生命周期）
     */
    var opacity: Double {
        let progress = age / lifespan
        return max(0, 1 - progress)
    }
}

/**
 * 盲盒面片数据模型（用于爆炸动画）
 */
struct CubeFace: Identifiable {
    let id = UUID()
    let type: FaceType
    var offset: CGPoint = .zero
    var rotation: Double = 0.0
    var scale: CGFloat = 1.0
    
    enum FaceType: CaseIterable {
        case front
        case back
        case left
        case right
        case top
        case bottom
        
        var description: String {
            switch self {
            case .front: return "正面"
            case .back: return "背面"
            case .left: return "左侧"
            case .right: return "右侧"
            case .top: return "顶部"
            case .bottom: return "底部"
            }
        }
    }
}

// MARK: - 扩展方法

extension BlindBoxItem {
    
    /**
     * 创建新的盲盒项目
     */
    static func create(index: Int, prizeName: String) -> BlindBoxItem {
        return BlindBoxItem(
            index: index,
            prizeName: prizeName
        )
    }
    
    /**
     * 创建已开启状态的盲盒项目（用于开箱后更新显示奖品）
     */
    static func createWithPrize(index: Int, prizeName: String, isOpened: Bool, explosionState: ExplosionState) -> BlindBoxItem {
        var item = BlindBoxItem(
            index: index,
            prizeName: prizeName
        )
        item.isOpened = isOpened
        item.explosionState = explosionState
        item.openingProgress = isOpened ? 1.0 : 0.0
        return item
    }
    
    /**
     * 开始开启动画
     */
    mutating func startOpening() {
        guard isClickable else { return }
        explosionState = .exploding
        openingProgress = 0.0
    }
    
    /**
     * 完成开启动画
     */
    mutating func completeOpening() {
        isOpened = true
        explosionState = .completed
        openingProgress = 1.0
    }
    
    /**
     * 重置动画状态
     */
    mutating func resetAnimation() {
        explosionState = .idle
        openingProgress = 0.0
        floatingOffset = 0.0
        rotationAngle = 0.0
        scaleEffect = 1.0
    }
}
